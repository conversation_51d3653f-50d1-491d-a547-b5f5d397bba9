{"name": "fula-chain", "private": true, "version": "1.0.0", "description": "Fula Blockchain EVM code", "scripts": {"lint": "eslint --color --ext .ts scripts/", "lint:fix": "yarn run lint --fix", "hardhat": "hardhat", "build": "hardhat compile", "hardhat:setpk": "yarn hardhat vars set PK", "hardhat:setbase": "yarn hardhat vars set BASESCAN_API_KEY", "deploy:localhost": "yarn hardhat run scripts/localhost.ts", "test:governance": "set REPORT_GAS=true && yarn hardhat test test/governance/GovernanceModule.test.ts", "test:token": "set REPORT_GAS=true && yarn hardhat test test/governance/integration/StorageToken.test.ts", "test:distribution": "set REPORT_GAS=true && yarn hardhat test test/governance/integration/TokenDistributionEngine.test.ts", "test:airdrop": "set REPORT_GAS=true && yarn hardhat test test/governance/integration/AirDropContract.test.ts", "test:mining": "set REPORT_GAS=true && yarn hardhat test test/governance/integration/TestnetMiningRewards.test.ts", "test:bridge": "set REPORT_GAS=true && yarn hardhat test test/governance/integration/TokenBridgeContract.test.ts", "test:staking": "set REPORT_GAS=true && yarn hardhat test test/governance/integration/StakingEngine.test.ts", "test:stakingpool": "set REPORT_GAS=true && yarn hardhat test test/governance/integration/StakingPool.test.ts", "test:stakinglinear": "set REPORT_GAS=true && yarn hardhat test test/governance/integration/StakingEngineLinear.test.ts", "test": "yarn hardhat test"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.4", "@nomicfoundation/hardhat-ethers": "^3.0.9", "@nomicfoundation/hardhat-ignition-ethers": "0.15.0", "@nomicfoundation/hardhat-network-helpers": "1.0.13", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "2.0.14", "@nomicfoundation/hardhat-web3-v4": "^1.0.0", "@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-waffle": "^2.0.6", "@openzeppelin/hardhat-upgrades": "^3.9.1", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.2.0", "@types/eslint": "^8", "@types/mocha": ">=9.1.0", "@types/node": "^22.10.2", "@types/yargs": "^17.0.33", "chai": "^4.2.0", "eslint": "^8", "ethereum-waffle": "^4.0.10", "ethers": "^6.15.0", "hardhat": "^2.25.0", "hardhat-gas-reporter": "2.3.0", "solidity-coverage": "^0.8.16", "ts-node": ">=8.0.0", "typechain": "^8.3.2", "typescript": ">=4.5.0", "web3": "^4.5.0"}, "packageManager": "yarn@3.8.0", "dependencies": {"@chainlink/contracts": "^1.4.0", "@nomicfoundation/hardhat-ignition": "^0.15.12", "@openzeppelin/contracts": "^5.3.0", "@openzeppelin/contracts-upgradeable": "^5.3.0", "@openzeppelin/upgrades-core": "^1.44.1", "@polkadot/util-crypto": "^13.5.3", "hardhat-contract-sizer": "^2.10.0", "tslib": "^2.8.1"}}